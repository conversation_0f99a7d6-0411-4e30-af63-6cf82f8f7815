<template>
  <header class="app-header bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">V3</span>
            </div>
            <span class="text-xl font-bold text-gray-900">Vue3 Template</span>
          </router-link>
        </div>

        <nav class="hidden md:flex space-x-8">
          <router-link
            v-for="item in visibleNavigation"
            :key="item.name"
            :to="item.href"
            class="nav-link"
            :class="{ 'nav-link-active': $route.path === item.href }"
          >
            {{ item.name }}
          </router-link>
        </nav>

        <div class="flex items-center space-x-4">
          <el-button @click="handleToggleTheme" circle>
            <el-icon><Moon v-if="currentTheme === 'light'" /><Sunny v-else /></el-icon>
          </el-button>

          <!-- 登录状态显示 -->
          <div v-if="userStore.isLoggedIn">
            <el-dropdown @command="handleUserCommand">
              <div class="flex items-center space-x-2 cursor-pointer">
                <el-avatar :size="32" src="/avatar.jpg" />
                <span class="hidden sm:block text-sm font-medium text-gray-700">
                  {{ currentUserName }}
                </span>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item disabled>
                    <div class="user-details">
                      <div class="user-name">{{ currentUserName }}</div>
                      <div class="user-role">{{ userRoleText }}</div>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item divided command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout" class="logout-item">
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 未登录状态 -->
          <div v-else class="auth-buttons">
            <el-button @click="goToLogin" type="primary" size="small">
              登录
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { Moon, Sunny } from '@element-plus/icons-vue'
import { useUserStore, useAppStore } from '@/stores'
import { usePermission } from '@/composables/usePermission'
import { Permission, UserRole } from '@/types/auth'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()
const { isMenuVisible } = usePermission()

const currentUserName = computed(() => userStore.userName)
const currentTheme = computed(() => appStore.theme)
const userRoleText = computed(() => {
  return userStore.userRoles.join(', ') || '无角色'
})

const navigation = [
  { name: 'Home', href: '/', isPublic: true },
  {
    name: 'Dashboard',
    href: '/dashboard',
    requireAuth: true,
    permissions: [Permission.DATA_VIEW]
  },
  {
    name: 'Charts',
    href: '/charts',
    requireAuth: true,
    permissions: [Permission.DATA_VIEW]
  },
  {
    name: 'Cache Demo',
    href: '/cache-demo',
    requireAuth: true,
    permissions: [Permission.CACHE_VIEW],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },
  {
    name: 'Permission Demo',
    href: '/permission-demo',
    requireAuth: true,
    permissions: [Permission.CACHE_VIEW],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },
]

// 过滤可见的导航项
const visibleNavigation = computed(() => {
  return navigation.filter(item => isMenuVisible(item))
})

const handleToggleTheme = () => {
  appStore.toggleTheme()
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style lang="scss" scoped>
.app-header {
  position: sticky;
  top: 0;
  z-index: 50;
}

.nav-link {
  @apply text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors;
}

.nav-link-active {
  @apply text-blue-600 bg-blue-50;
}

.user-details {
  padding: 4px 0;

  .user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
  }

  .user-role {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.logout-item {
  color: #f56c6c !important;

  &:hover {
    background-color: #fef0f0 !important;
  }
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
