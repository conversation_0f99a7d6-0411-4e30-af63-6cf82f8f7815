import type { RoutePermission, RouteGuardConfig } from '@/types/auth'
import { Permission, UserRole } from '@/types/auth'

/**
 * 路由权限配置
 */
export const routePermissions: RoutePermission[] = [
  // 公开路由
  {
    path: '/',
    isPublic: true,
    requireAuth: false
  },
  {
    path: '/login',
    isPublic: true,
    requireAuth: false
  },
  {
    path: '/register',
    isPublic: true,
    requireAuth: false
  },
  {
    path: '/404',
    isPublic: true,
    requireAuth: false
  },
  {
    path: '/403',
    isPublic: true,
    requireAuth: false
  },

  // 需要登录的路由
  {
    path: '/dashboard',
    requireAuth: true,
    permissions: [Permission.DATA_VIEW]
  },
  {
    path: '/charts',
    requireAuth: true,
    permissions: [Permission.DATA_VIEW]
  },

  // 缓存管理相关路由
  {
    path: '/cache-demo',
    requireAuth: true,
    permissions: [Permission.CACHE_VIEW],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },

  // 用户管理路由
  {
    path: '/users',
    requireAuth: true,
    permissions: [Permission.USER_VIEW],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },
  {
    path: '/users/create',
    requireAuth: true,
    permissions: [Permission.USER_CREATE],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },
  {
    path: '/users/:id/edit',
    requireAuth: true,
    permissions: [Permission.USER_UPDATE],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },

  // 系统管理路由
  {
    path: '/system',
    requireAuth: true,
    permissions: [Permission.SYSTEM_CONFIG],
    roles: [UserRole.SUPER_ADMIN]
  },
  {
    path: '/system/config',
    requireAuth: true,
    permissions: [Permission.SYSTEM_CONFIG],
    roles: [UserRole.SUPER_ADMIN]
  },
  {
    path: '/system/logs',
    requireAuth: true,
    permissions: [Permission.SYSTEM_LOG],
    roles: [UserRole.SUPER_ADMIN]
  },
  {
    path: '/system/monitor',
    requireAuth: true,
    permissions: [Permission.SYSTEM_MONITOR],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },

  // 数据管理路由
  {
    path: '/data',
    requireAuth: true,
    permissions: [Permission.DATA_VIEW]
  },
  {
    path: '/data/export',
    requireAuth: true,
    permissions: [Permission.DATA_EXPORT],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },
  {
    path: '/data/import',
    requireAuth: true,
    permissions: [Permission.DATA_IMPORT],
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
  },

  // 个人中心路由
  {
    path: '/profile',
    requireAuth: true
  },
  {
    path: '/settings',
    requireAuth: true
  }
]

/**
 * 路由守卫配置
 */
export const routeGuardConfig: RouteGuardConfig = {
  enablePermissionCheck: true,
  enableRoleCheck: true,
  defaultRedirectPath: '/dashboard',
  loginPath: '/login',
  noPermissionPath: '/403',
  whiteList: [
    '/',
    '/login',
    '/register',
    '/404',
    '/403',
    '/about'
  ]
}

/**
 * 角色权限映射
 */
export const rolePermissionMap: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: [
    // 用户管理权限
    Permission.USER_VIEW,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    Permission.USER_DELETE,
    
    // 系统管理权限
    Permission.SYSTEM_CONFIG,
    Permission.SYSTEM_LOG,
    Permission.SYSTEM_MONITOR,
    
    // 数据权限
    Permission.DATA_VIEW,
    Permission.DATA_EXPORT,
    Permission.DATA_IMPORT,
    
    // 缓存管理权限
    Permission.CACHE_VIEW,
    Permission.CACHE_MANAGE,
    Permission.CACHE_CLEAR,
  ],
  
  [UserRole.ADMIN]: [
    // 用户管理权限
    Permission.USER_VIEW,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    
    // 系统监控权限
    Permission.SYSTEM_MONITOR,
    
    // 数据权限
    Permission.DATA_VIEW,
    Permission.DATA_EXPORT,
    Permission.DATA_IMPORT,
    
    // 缓存查看权限
    Permission.CACHE_VIEW,
    Permission.CACHE_MANAGE,
  ],
  
  [UserRole.USER]: [
    // 基础数据查看权限
    Permission.DATA_VIEW,
    
    // 缓存查看权限
    Permission.CACHE_VIEW,
  ],
  
  [UserRole.GUEST]: [
    // 仅基础查看权限
    Permission.DATA_VIEW,
  ]
}

/**
 * 根据角色获取权限列表
 */
export const getPermissionsByRole = (role: UserRole): Permission[] => {
  return rolePermissionMap[role] || []
}

/**
 * 根据角色列表获取所有权限
 */
export const getPermissionsByRoles = (roles: UserRole[]): Permission[] => {
  const allPermissions = new Set<Permission>()
  
  roles.forEach(role => {
    const permissions = getPermissionsByRole(role)
    permissions.forEach(permission => allPermissions.add(permission))
  })
  
  return Array.from(allPermissions)
}

/**
 * 检查路径是否在白名单中
 */
export const isWhiteListRoute = (path: string): boolean => {
  return routeGuardConfig.whiteList.some(whiteListPath => {
    // 支持通配符匹配
    if (whiteListPath.includes('*')) {
      const regex = new RegExp(whiteListPath.replace(/\*/g, '.*'))
      return regex.test(path)
    }
    return path === whiteListPath
  })
}

/**
 * 获取路由的权限配置
 */
export const getRoutePermissionConfig = (path: string): RoutePermission | undefined => {
  return routePermissions.find(route => {
    // 支持动态路由匹配
    if (route.path.includes(':')) {
      const regex = new RegExp(route.path.replace(/:[\w]+/g, '[^/]+'))
      return regex.test(path)
    }
    return route.path === path
  })
}

/**
 * 路由权限预设配置
 */
export const routePermissionPresets = {
  // 管理员路由预设
  adminRoutes: [
    '/dashboard',
    '/users',
    '/users/*',
    '/system/monitor',
    '/data',
    '/data/export',
    '/data/import',
    '/cache-demo'
  ],
  
  // 超级管理员路由预设
  superAdminRoutes: [
    '/system',
    '/system/*',
    '/users/*/delete'
  ],
  
  // 普通用户路由预设
  userRoutes: [
    '/dashboard',
    '/charts',
    '/profile',
    '/settings'
  ],
  
  // 访客路由预设
  guestRoutes: [
    '/',
    '/about'
  ]
}
