<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-animation">
        <div class="error-icon">
          <el-icon size="120" color="#909399">
            <QuestionFilled />
          </el-icon>
        </div>
        <div class="floating-elements">
          <div class="element element-1"></div>
          <div class="element element-2"></div>
          <div class="element element-3"></div>
        </div>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">404</h1>
        <h2 class="error-subtitle">页面不存在</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        <div class="search-suggestion">
          <p>您可能在寻找：</p>
          <div class="suggestion-links">
            <router-link 
              v-for="link in suggestedLinks" 
              :key="link.path"
              :to="link.path"
              class="suggestion-link"
            >
              <el-icon>
                <component :is="link.icon" />
              </el-icon>
              {{ link.name }}
            </router-link>
          </div>
        </div>
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
        <el-button @click="goHome">
          <el-icon><House /></el-icon>
          回到首页
        </el-button>
        <el-button @click="reportIssue" type="info">
          <el-icon><Warning /></el-icon>
          报告问题
        </el-button>
      </div>

      <div class="error-stats">
        <el-card class="stats-card">
          <template #header>
            <span>访问统计</span>
          </template>
          <div class="stats-content">
            <div class="stat-item">
              <div class="stat-label">当前路径</div>
              <div class="stat-value">{{ currentPath }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">访问时间</div>
              <div class="stat-value">{{ accessTime }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">来源页面</div>
              <div class="stat-value">{{ referrer }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  QuestionFilled, 
  ArrowLeft, 
  House, 
  Warning,
  DataBoard,
  TrendCharts,
  Setting,
  User
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const accessTime = ref('')
const referrer = ref('')

const currentPath = computed(() => route.fullPath)

const suggestedLinks = [
  {
    name: '仪表板',
    path: '/dashboard',
    icon: 'DataBoard'
  },
  {
    name: '图表',
    path: '/charts', 
    icon: 'TrendCharts'
  },
  {
    name: '缓存演示',
    path: '/cache-demo',
    icon: 'Setting'
  },
  {
    name: '用户中心',
    path: '/profile',
    icon: 'User'
  }
]

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

const goHome = () => {
  router.push('/')
}

const reportIssue = () => {
  const issueData = {
    path: route.fullPath,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
  }
  
  console.log('404 Issue Report:', issueData)
  ElMessage.success('问题已记录，感谢您的反馈')
  
  // 这里可以发送到错误收集服务
}

onMounted(() => {
  accessTime.value = new Date().toLocaleString()
  referrer.value = document.referrer || '直接访问'
  
  // 记录 404 访问日志
  console.warn('404 Page Not Found:', {
    path: route.fullPath,
    timestamp: new Date().toISOString(),
    referrer: document.referrer,
    userAgent: navigator.userAgent
  })
})
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.error-content {
  text-align: center;
  max-width: 700px;
  width: 100%;
  position: relative;
  z-index: 2;
}

.error-animation {
  position: relative;
  margin-bottom: 30px;

  .error-icon {
    animation: float 3s ease-in-out infinite;
  }

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .element {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float-random 4s ease-in-out infinite;

      &.element-1 {
        width: 20px;
        height: 20px;
        top: 20%;
        left: 20%;
        animation-delay: 0s;
      }

      &.element-2 {
        width: 15px;
        height: 15px;
        top: 60%;
        right: 30%;
        animation-delay: 1s;
      }

      &.element-3 {
        width: 25px;
        height: 25px;
        bottom: 30%;
        left: 70%;
        animation-delay: 2s;
      }
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-random {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(10px, -10px) rotate(90deg); }
  50% { transform: translate(-5px, -20px) rotate(180deg); }
  75% { transform: translate(-10px, -5px) rotate(270deg); }
}

.error-info {
  margin-bottom: 40px;
  color: white;

  .error-title {
    font-size: 96px;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .error-subtitle {
    font-size: 36px;
    margin: 0 0 20px 0;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  .error-description {
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 30px 0;
    opacity: 0.9;
  }
}

.search-suggestion {
  margin: 30px 0;
  
  p {
    margin-bottom: 15px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
  }

  .suggestion-links {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;

    .suggestion-link {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      text-decoration: none;
      border-radius: 20px;
      font-size: 14px;
      transition: all 0.3s;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }
    }
  }
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;

  .el-button {
    min-width: 120px;
    backdrop-filter: blur(10px);
  }
}

.error-stats {
  margin-top: 30px;

  .stats-card {
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);

    .stats-content {
      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .stat-label {
          font-weight: 600;
          color: #333;
        }

        .stat-value {
          color: #666;
          font-size: 14px;
          max-width: 200px;
          word-break: break-all;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-info {
    .error-title {
      font-size: 64px;
    }

    .error-subtitle {
      font-size: 28px;
    }
  }

  .error-actions {
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 200px;
    }
  }

  .search-suggestion {
    .suggestion-links {
      flex-direction: column;
      align-items: center;

      .suggestion-link {
        width: 200px;
        justify-content: center;
      }
    }
  }
}
</style>
