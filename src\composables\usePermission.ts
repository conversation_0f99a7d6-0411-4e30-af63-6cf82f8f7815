import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
import { permissionManager } from '@/utils/permission'
import type { Permission, UserRole, PermissionCheckResult } from '@/types/auth'

/**
 * 权限管理 Composable
 */
export function usePermission() {
  const userStore = useUserStore()
  const router = useRouter()
  const route = useRoute()

  // 权限检查结果
  const permissionCheckResult = ref<PermissionCheckResult | null>(null)

  // 当前用户权限
  const userPermissions = computed(() => userStore.userPermissions)
  const userRoles = computed(() => userStore.userRoles)

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: Permission): boolean => {
    return userStore.hasPermission(permission)
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: UserRole): boolean => {
    return userStore.hasRole(role)
  }

  /**
   * 检查是否有任一权限
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return userStore.hasAnyPermission(permissions)
  }

  /**
   * 检查是否有所有权限
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return userStore.hasAllPermissions(permissions)
  }

  /**
   * 检查是否有任一角色
   */
  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.some(role => hasRole(role))
  }

  /**
   * 检查当前路由权限
   */
  const checkCurrentRoutePermission = (): PermissionCheckResult => {
    const result = permissionManager.checkRoutePermission(route.path)
    permissionCheckResult.value = result
    return result
  }

  /**
   * 检查指定路由权限
   */
  const checkRoutePermission = (routePath: string): PermissionCheckResult => {
    return permissionManager.checkRoutePermission(routePath)
  }

  /**
   * 权限守卫 - 检查权限并处理跳转
   */
  const guardPermission = (
    requiredPermissions: Permission[],
    options: {
      redirectTo?: string
      showMessage?: boolean
      throwError?: boolean
    } = {}
  ): boolean => {
    const { redirectTo = '/403', showMessage = true, throwError = false } = options

    if (!hasAllPermissions(requiredPermissions)) {
      const missingPermissions = requiredPermissions.filter(p => !hasPermission(p))
      
      if (showMessage) {
        console.warn('Permission denied:', { 
          required: requiredPermissions, 
          missing: missingPermissions 
        })
      }

      if (throwError) {
        throw new Error(`Missing permissions: ${missingPermissions.join(', ')}`)
      }

      if (redirectTo) {
        router.push({
          path: redirectTo,
          query: {
            from: route.fullPath,
            reason: 'missing_permissions'
          }
        })
      }

      return false
    }

    return true
  }

  /**
   * 角色守卫 - 检查角色并处理跳转
   */
  const guardRole = (
    requiredRoles: UserRole[],
    options: {
      redirectTo?: string
      showMessage?: boolean
      throwError?: boolean
    } = {}
  ): boolean => {
    const { redirectTo = '/403', showMessage = true, throwError = false } = options

    if (!hasAnyRole(requiredRoles)) {
      if (showMessage) {
        console.warn('Role access denied:', { 
          required: requiredRoles, 
          current: userRoles.value 
        })
      }

      if (throwError) {
        throw new Error(`Missing roles: ${requiredRoles.join(', ')}`)
      }

      if (redirectTo) {
        router.push({
          path: redirectTo,
          query: {
            from: route.fullPath,
            reason: 'insufficient_role'
          }
        })
      }

      return false
    }

    return true
  }

  /**
   * 获取可访问的路由列表
   */
  const getAccessibleRoutes = () => {
    const allRoutes = router.getRoutes()
    return allRoutes.filter(route => {
      if (!route.path || route.path === '*') return false
      const result = permissionManager.checkRoutePermission(route.path)
      return result.hasPermission
    })
  }

  /**
   * 检查菜单项是否可见
   */
  const isMenuVisible = (menuItem: {
    permissions?: Permission[]
    roles?: UserRole[]
    requireAuth?: boolean
  }): boolean => {
    // 如果需要登录但用户未登录
    if (menuItem.requireAuth && !userStore.isLoggedIn) {
      return false
    }

    // 检查权限
    if (menuItem.permissions && menuItem.permissions.length > 0) {
      if (!hasAnyPermission(menuItem.permissions)) {
        return false
      }
    }

    // 检查角色
    if (menuItem.roles && menuItem.roles.length > 0) {
      if (!hasAnyRole(menuItem.roles)) {
        return false
      }
    }

    return true
  }

  /**
   * 权限变更监听
   */
  const onPermissionChange = (callback: () => void) => {
    const stopWatcher = watch(
      [userPermissions, userRoles],
      () => {
        callback()
      },
      { deep: true }
    )

    return stopWatcher
  }

  /**
   * 刷新权限
   */
  const refreshPermissions = async () => {
    try {
      // 这里应该调用 API 重新获取用户权限
      // const userInfo = await getUserInfo()
      // userStore.setPermissions(userInfo.permissions)
      // userStore.setRoles(userInfo.roles)
      
      console.log('Permissions refreshed')
    } catch (error) {
      console.error('Failed to refresh permissions:', error)
      throw error
    }
  }

  return {
    // 状态
    userPermissions,
    userRoles,
    permissionCheckResult,

    // 权限检查方法
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,

    // 路由权限检查
    checkCurrentRoutePermission,
    checkRoutePermission,

    // 权限守卫
    guardPermission,
    guardRole,

    // 工具方法
    getAccessibleRoutes,
    isMenuVisible,
    onPermissionChange,
    refreshPermissions,
  }
}

/**
 * 权限指令 Composable
 */
export function usePermissionDirective() {
  const { hasPermission, hasRole, hasAnyPermission, hasAnyRole } = usePermission()

  /**
   * v-permission 指令实现
   */
  const vPermission = {
    mounted(el: HTMLElement, binding: any) {
      const { value, modifiers } = binding
      
      let hasAccess = false

      if (Array.isArray(value)) {
        // 数组形式：检查是否有任一权限
        hasAccess = modifiers.all 
          ? value.every(p => hasPermission(p))
          : hasAnyPermission(value)
      } else if (typeof value === 'string') {
        // 字符串形式：检查单个权限
        hasAccess = hasPermission(value as Permission)
      }

      if (!hasAccess) {
        // 移除元素或隐藏
        if (modifiers.hide) {
          el.style.display = 'none'
        } else {
          el.parentNode?.removeChild(el)
        }
      }
    },

    updated(el: HTMLElement, binding: any) {
      // 权限变更时重新检查
      const { value, modifiers } = binding
      
      let hasAccess = false

      if (Array.isArray(value)) {
        hasAccess = modifiers.all 
          ? value.every(p => hasPermission(p))
          : hasAnyPermission(value)
      } else if (typeof value === 'string') {
        hasAccess = hasPermission(value as Permission)
      }

      if (hasAccess) {
        el.style.display = ''
      } else {
        if (modifiers.hide) {
          el.style.display = 'none'
        } else {
          el.parentNode?.removeChild(el)
        }
      }
    }
  }

  /**
   * v-role 指令实现
   */
  const vRole = {
    mounted(el: HTMLElement, binding: any) {
      const { value, modifiers } = binding
      
      let hasAccess = false

      if (Array.isArray(value)) {
        hasAccess = modifiers.all 
          ? value.every(r => hasRole(r))
          : hasAnyRole(value)
      } else if (typeof value === 'string') {
        hasAccess = hasRole(value as UserRole)
      }

      if (!hasAccess) {
        if (modifiers.hide) {
          el.style.display = 'none'
        } else {
          el.parentNode?.removeChild(el)
        }
      }
    },

    updated(el: HTMLElement, binding: any) {
      const { value, modifiers } = binding
      
      let hasAccess = false

      if (Array.isArray(value)) {
        hasAccess = modifiers.all 
          ? value.every(r => hasRole(r))
          : hasAnyRole(value)
      } else if (typeof value === 'string') {
        hasAccess = hasRole(value as UserRole)
      }

      if (hasAccess) {
        el.style.display = ''
      } else {
        if (modifiers.hide) {
          el.style.display = 'none'
        } else {
          el.parentNode?.removeChild(el)
        }
      }
    }
  }

  return {
    vPermission,
    vRole
  }
}
