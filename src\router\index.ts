import { createRouter, createWebHistory } from "vue-router"
import type { RouteRecordRaw } from "vue-router"
import { setupRouteGuards } from "./guards"
import { permissionManager } from "@/utils/permission"
import { routePermissions } from "@/config/routes"

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/Home.vue"),
    meta: {
      title: "首页",
      requireAuth: false,
      isPublic: true
    }
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    meta: {
      title: "登录",
      requireAuth: false,
      isPublic: true
    }
  },
  {
    path: "/dashboard",
    name: "Dashboard",
    component: () => import("@/views/Dashboard.vue"),
    meta: {
      title: "仪表板",
      requireAuth: true
    }
  },
  {
    path: "/charts",
    name: "Charts",
    component: () => import("@/views/Charts.vue"),
    meta: {
      title: "图表",
      requireAuth: true
    }
  },
  {
    path: "/cache-demo",
    name: "CacheDemo",
    component: () => import("@/views/CacheDemo.vue"),
    meta: {
      title: "缓存演示",
      requireAuth: true,
      permissions: ["cache:view"]
    }
  },
  {
    path: "/permission-demo",
    name: "PermissionDemo",
    component: () => import("@/views/PermissionDemo.vue"),
    meta: {
      title: "权限演示",
      requireAuth: true,
      permissions: ["cache:view"]
    }
  },
  {
    path: "/403",
    name: "Forbidden",
    component: () => import("@/views/403.vue"),
    meta: {
      title: "无权限",
      isPublic: true
    }
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/404.vue"),
    meta: {
      title: "页面不存在",
      isPublic: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 注册路由权限配置
permissionManager.registerRoutePermissions(routePermissions)

// 安装路由守卫
setupRouteGuards(router)

export default router
