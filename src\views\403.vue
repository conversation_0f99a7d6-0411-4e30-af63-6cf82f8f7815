<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-icon">
        <el-icon size="120" color="#f56c6c">
          <Lock />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">403</h1>
        <h2 class="error-subtitle">访问被拒绝</h2>
        <p class="error-description">
          抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
        </p>
        
        <div class="error-details" v-if="errorDetails">
          <el-collapse>
            <el-collapse-item title="错误详情" name="details">
              <div class="details-content">
                <p><strong>来源页面:</strong> {{ errorDetails.from }}</p>
                <p><strong>目标页面:</strong> {{ errorDetails.to }}</p>
                <p><strong>错误原因:</strong> {{ errorDetails.reason }}</p>
                <p><strong>时间:</strong> {{ errorDetails.timestamp }}</p>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
        <el-button @click="goHome">
          <el-icon><House /></el-icon>
          回到首页
        </el-button>
        <el-button @click="goLogin" v-if="!userStore.isLoggedIn">
          <el-icon><User /></el-icon>
          重新登录
        </el-button>
        <el-button @click="contactAdmin" type="info">
          <el-icon><Message /></el-icon>
          联系管理员
        </el-button>
      </div>

      <div class="permission-info" v-if="userStore.isLoggedIn">
        <el-card class="info-card">
          <template #header>
            <span>当前用户信息</span>
          </template>
          <div class="user-info">
            <p><strong>用户名:</strong> {{ userStore.userName }}</p>
            <p><strong>角色:</strong> {{ userRoles }}</p>
            <p><strong>权限数量:</strong> {{ userStore.userPermissions.length }}</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Lock, ArrowLeft, House, User, Message } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const errorDetails = ref<{
  from: string
  to: string
  reason: string
  timestamp: string
} | null>(null)

const userRoles = computed(() => {
  return userStore.userRoles.join(', ') || '无角色'
})

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

const goHome = () => {
  router.push('/')
}

const goLogin = () => {
  router.push('/login')
}

const contactAdmin = () => {
  ElMessage.info('请联系系统管理员获取帮助')
  // 这里可以集成客服系统或发送邮件
}

onMounted(() => {
  // 从路由查询参数中获取错误详情
  const { from, reason } = route.query
  
  if (from || reason) {
    errorDetails.value = {
      from: from as string || '未知',
      to: route.fullPath,
      reason: reason as string || '权限不足',
      timestamp: new Date().toLocaleString()
    }
  }

  // 记录访问日志
  console.warn('403 Access Denied:', {
    path: route.fullPath,
    user: userStore.userName,
    roles: userStore.userRoles,
    permissions: userStore.userPermissions.length,
    timestamp: new Date().toISOString()
  })
})
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  margin-bottom: 30px;
  animation: shake 2s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.error-info {
  margin-bottom: 40px;

  .error-title {
    font-size: 72px;
    font-weight: bold;
    color: #f56c6c;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .error-subtitle {
    font-size: 32px;
    color: #333;
    margin: 0 0 20px 0;
    font-weight: 600;
  }

  .error-description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 30px 0;
  }
}

.error-details {
  margin: 20px 0;
  text-align: left;

  .details-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    font-size: 14px;

    p {
      margin: 8px 0;
      
      strong {
        color: #333;
      }
    }
  }
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;

  .el-button {
    min-width: 120px;
  }
}

.permission-info {
  margin-top: 30px;

  .info-card {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;

    .user-info {
      p {
        margin: 8px 0;
        font-size: 14px;

        strong {
          color: #333;
          display: inline-block;
          width: 80px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-info {
    .error-title {
      font-size: 48px;
    }

    .error-subtitle {
      font-size: 24px;
    }
  }

  .error-actions {
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 200px;
    }
  }
}
</style>
