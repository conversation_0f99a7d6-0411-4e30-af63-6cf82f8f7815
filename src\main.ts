import { createApp } from "vue"
import { createPinia } from "pinia"
import ElementPlus from "element-plus"
import "element-plus/dist/index.css"
import "./styles/index.scss"
import App from "./App.vue"
import router from "./router"
import { usePermissionDirective } from "./composables/usePermission"

const app = createApp(App)
const pinia = createPinia()

// 注册权限指令
const { vPermission, vRole } = usePermissionDirective()
app.directive('permission', vPermission)
app.directive('role', vRole)

app.use(pinia)
app.use(router)
app.use(ElementPlus)

app.mount("#app")

// 初始化用户认证和权限
setTimeout(() => {
  import("./stores").then(({ useUserStore }) => {
    const userStore = useUserStore()
    userStore.initAuth()
  })
}, 0)
