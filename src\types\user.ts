import type { Permission, UserRole } from './auth'

export interface User {
  id: string
  name: string
  username: string
  email: string
  avatar?: string
  role: string // 保留原有的 role 字段以兼容
  roles: UserRole[] // 新的角色数组
  permissions: Permission[] // 用户权限
  createdAt: string
  updatedAt: string
  status?: 'active' | 'inactive' | 'suspended'
  lastLoginAt?: string
}

export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
}
