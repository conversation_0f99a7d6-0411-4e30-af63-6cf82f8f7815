<template>
  <div class="permission-demo-container">
    <AppHeader />
    
    <div class="permission-demo-content p-6">
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">权限系统演示</h1>
        <p class="text-gray-600">演示基于角色的权限控制系统功能</p>
      </div>

      <!-- 当前用户信息 -->
      <el-card class="mb-6">
        <template #header>
          <span>当前用户信息</span>
        </template>
        
        <div class="user-info-grid">
          <div class="info-item">
            <div class="info-label">用户名</div>
            <div class="info-value">{{ userStore.userName }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">角色</div>
            <div class="info-value">
              <el-tag 
                v-for="role in userStore.userRoles" 
                :key="role" 
                :type="getRoleTagType(role)"
                size="small"
              >
                {{ getRoleLabel(role) }}
              </el-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">权限数量</div>
            <div class="info-value">{{ userStore.userPermissions.length }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">登录状态</div>
            <div class="info-value">
              <el-tag :type="userStore.isLoggedIn ? 'success' : 'danger'" size="small">
                {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 权限检查演示 -->
      <el-card class="mb-6">
        <template #header>
          <span>权限检查演示</span>
        </template>
        
        <div class="permission-checks">
          <div class="check-section">
            <h3>单个权限检查</h3>
            <div class="permission-list">
              <div 
                v-for="permission in allPermissions" 
                :key="permission"
                class="permission-item"
              >
                <div class="permission-name">{{ getPermissionLabel(permission) }}</div>
                <el-tag 
                  :type="hasPermission(permission) ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ hasPermission(permission) ? '有权限' : '无权限' }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="check-section">
            <h3>角色检查</h3>
            <div class="role-list">
              <div 
                v-for="role in allRoles" 
                :key="role"
                class="role-item"
              >
                <div class="role-name">{{ getRoleLabel(role) }}</div>
                <el-tag 
                  :type="hasRole(role) ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ hasRole(role) ? '拥有角色' : '无此角色' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 权限指令演示 -->
      <el-card class="mb-6">
        <template #header>
          <span>权限指令演示</span>
        </template>
        
        <div class="directive-demo">
          <div class="demo-section">
            <h3>v-permission 指令</h3>
            <div class="demo-buttons">
              <el-button v-permission="Permission.USER_VIEW" type="primary">
                查看用户 (需要 USER_VIEW 权限)
              </el-button>
              <el-button v-permission="Permission.USER_CREATE" type="success">
                创建用户 (需要 USER_CREATE 权限)
              </el-button>
              <el-button v-permission="Permission.SYSTEM_CONFIG" type="warning">
                系统配置 (需要 SYSTEM_CONFIG 权限)
              </el-button>
              <el-button v-permission="[Permission.DATA_EXPORT, Permission.DATA_IMPORT]" type="info">
                数据操作 (需要任一权限)
              </el-button>
            </div>
          </div>

          <div class="demo-section">
            <h3>v-role 指令</h3>
            <div class="demo-buttons">
              <el-button v-role="UserRole.ADMIN" type="primary">
                管理员功能 (需要 ADMIN 角色)
              </el-button>
              <el-button v-role="UserRole.SUPER_ADMIN" type="danger">
                超级管理员功能 (需要 SUPER_ADMIN 角色)
              </el-button>
              <el-button v-role="[UserRole.USER, UserRole.ADMIN]" type="success">
                用户功能 (需要 USER 或 ADMIN 角色)
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 路由权限演示 -->
      <el-card class="mb-6">
        <template #header>
          <span>路由权限演示</span>
        </template>
        
        <div class="route-demo">
          <div class="route-list">
            <div 
              v-for="route in testRoutes" 
              :key="route.path"
              class="route-item"
            >
              <div class="route-info">
                <div class="route-path">{{ route.path }}</div>
                <div class="route-desc">{{ route.description }}</div>
              </div>
              <div class="route-status">
                <el-tag 
                  :type="checkRouteAccess(route.path) ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ checkRouteAccess(route.path) ? '可访问' : '无权限' }}
                </el-tag>
                <el-button 
                  size="small" 
                  @click="testRouteAccess(route.path)"
                  :disabled="!checkRouteAccess(route.path)"
                >
                  测试访问
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 权限切换演示 -->
      <el-card>
        <template #header>
          <span>权限切换演示</span>
        </template>
        
        <div class="role-switch">
          <p class="mb-4">切换不同角色来体验权限变化：</p>
          <div class="switch-buttons">
            <el-button 
              v-for="role in allRoles" 
              :key="role"
              @click="switchRole(role)"
              :type="userStore.userRoles.includes(role) ? 'primary' : 'default'"
            >
              切换到{{ getRoleLabel(role) }}
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import AppHeader from '@/components/AppHeader.vue'
import { useUserStore } from '@/stores'
import { usePermission } from '@/composables/usePermission'
import { Permission, UserRole } from '@/types/auth'
import { getPermissionsByRole } from '@/config/routes'

const router = useRouter()
const userStore = useUserStore()
const { hasPermission, hasRole, checkRoutePermission } = usePermission()

// 所有权限和角色
const allPermissions = Object.values(Permission)
const allRoles = Object.values(UserRole)

// 测试路由
const testRoutes = [
  { path: '/dashboard', description: '仪表板 - 需要 DATA_VIEW 权限' },
  { path: '/cache-demo', description: '缓存演示 - 需要 CACHE_VIEW 权限和管理员角色' },
  { path: '/system/config', description: '系统配置 - 需要 SYSTEM_CONFIG 权限' },
  { path: '/users', description: '用户管理 - 需要 USER_VIEW 权限' },
]

const getRoleTagType = (role: UserRole) => {
  const typeMap = {
    [UserRole.SUPER_ADMIN]: 'danger',
    [UserRole.ADMIN]: 'warning',
    [UserRole.USER]: 'success',
    [UserRole.GUEST]: 'info'
  }
  return typeMap[role] || 'default'
}

const getRoleLabel = (role: UserRole) => {
  const labelMap = {
    [UserRole.SUPER_ADMIN]: '超级管理员',
    [UserRole.ADMIN]: '管理员',
    [UserRole.USER]: '普通用户',
    [UserRole.GUEST]: '访客'
  }
  return labelMap[role] || role
}

const getPermissionLabel = (permission: Permission) => {
  const labelMap = {
    [Permission.USER_VIEW]: '查看用户',
    [Permission.USER_CREATE]: '创建用户',
    [Permission.USER_UPDATE]: '更新用户',
    [Permission.USER_DELETE]: '删除用户',
    [Permission.SYSTEM_CONFIG]: '系统配置',
    [Permission.SYSTEM_LOG]: '系统日志',
    [Permission.SYSTEM_MONITOR]: '系统监控',
    [Permission.DATA_VIEW]: '查看数据',
    [Permission.DATA_EXPORT]: '导出数据',
    [Permission.DATA_IMPORT]: '导入数据',
    [Permission.CACHE_VIEW]: '查看缓存',
    [Permission.CACHE_MANAGE]: '管理缓存',
    [Permission.CACHE_CLEAR]: '清理缓存',
  }
  return labelMap[permission] || permission
}

const checkRouteAccess = (path: string) => {
  const result = checkRoutePermission(path)
  return result.hasPermission
}

const testRouteAccess = (path: string) => {
  if (checkRouteAccess(path)) {
    router.push(path)
  } else {
    ElMessage.error('您没有权限访问此页面')
  }
}

const switchRole = (role: UserRole) => {
  // 模拟角色切换
  const permissions = getPermissionsByRole(role)
  userStore.setRoles([role])
  userStore.setPermissions(permissions)
  
  ElMessage.success(`已切换到${getRoleLabel(role)}角色`)
}
</script>

<style lang="scss" scoped>
.permission-demo-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.user-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;

  .info-item {
    .info-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .info-value {
      font-size: 16px;
      font-weight: 600;
      color: #333;

      .el-tag {
        margin-right: 8px;
      }
    }
  }
}

.permission-checks {
  .check-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      color: #333;
    }

    .permission-list,
    .role-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 12px;
    }

    .permission-item,
    .role-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;

      .permission-name,
      .role-name {
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.directive-demo {
  .demo-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      color: #333;
    }

    .demo-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
}

.route-demo {
  .route-list {
    .route-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 12px;

      .route-info {
        .route-path {
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .route-desc {
          font-size: 14px;
          color: #666;
        }
      }

      .route-status {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
}

.role-switch {
  .switch-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}
</style>
