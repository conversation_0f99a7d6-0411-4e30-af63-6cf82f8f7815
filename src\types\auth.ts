/**
 * 权限相关类型定义
 */

/**
 * 用户角色枚举
 */
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

/**
 * 权限枚举
 */
export enum Permission {
  // 用户管理权限
  USER_VIEW = 'user:view',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  
  // 系统管理权限
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_LOG = 'system:log',
  SYSTEM_MONITOR = 'system:monitor',
  
  // 数据权限
  DATA_VIEW = 'data:view',
  DATA_EXPORT = 'data:export',
  DATA_IMPORT = 'data:import',
  
  // 缓存管理权限
  CACHE_VIEW = 'cache:view',
  CACHE_MANAGE = 'cache:manage',
  CACHE_CLEAR = 'cache:clear',
}

/**
 * 路由权限配置
 */
export interface RoutePermission {
  /** 路由路径 */
  path: string
  /** 需要的权限列表 */
  permissions?: Permission[]
  /** 需要的角色列表 */
  roles?: UserRole[]
  /** 是否需要登录 */
  requireAuth?: boolean
  /** 是否为公开路由 */
  isPublic?: boolean
}

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  /** 是否有权限 */
  hasPermission: boolean
  /** 缺少的权限 */
  missingPermissions?: Permission[]
  /** 缺少的角色 */
  missingRoles?: UserRole[]
  /** 错误信息 */
  message?: string
}

/**
 * 路由守卫配置
 */
export interface RouteGuardConfig {
  /** 是否启用权限检查 */
  enablePermissionCheck: boolean
  /** 是否启用角色检查 */
  enableRoleCheck: boolean
  /** 默认重定向路径 */
  defaultRedirectPath: string
  /** 登录页面路径 */
  loginPath: string
  /** 无权限页面路径 */
  noPermissionPath: string
  /** 白名单路由（不需要权限检查） */
  whiteList: string[]
}

/**
 * 权限管理器接口
 */
export interface PermissionManager {
  /** 检查用户是否有指定权限 */
  hasPermission(permission: Permission): boolean
  /** 检查用户是否有指定角色 */
  hasRole(role: UserRole): boolean
  /** 检查用户是否有任一权限 */
  hasAnyPermission(permissions: Permission[]): boolean
  /** 检查用户是否有所有权限 */
  hasAllPermissions(permissions: Permission[]): boolean
  /** 检查路由权限 */
  checkRoutePermission(routePath: string): PermissionCheckResult
  /** 获取用户权限列表 */
  getUserPermissions(): Permission[]
  /** 获取用户角色列表 */
  getUserRoles(): UserRole[]
}

/**
 * 权限变更事件
 */
export interface PermissionChangeEvent {
  type: 'permission_added' | 'permission_removed' | 'role_changed' | 'user_logout'
  payload: {
    permissions?: Permission[]
    roles?: UserRole[]
    userId?: string
  }
  timestamp: number
}

/**
 * 权限缓存配置
 */
export interface PermissionCacheConfig {
  /** 权限缓存时间（毫秒） */
  permissionCacheTTL: number
  /** 角色缓存时间（毫秒） */
  roleCacheTTL: number
  /** 是否启用权限缓存 */
  enableCache: boolean
  /** 缓存键前缀 */
  cacheKeyPrefix: string
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 令牌过期时间 */
  expiresIn: number
  /** 用户信息 */
  user: {
    id: string
    username: string
    email: string
    avatar?: string
    roles: UserRole[]
    permissions: Permission[]
  }
}

/**
 * 权限验证中间件配置
 */
export interface PermissionMiddlewareConfig {
  /** 是否启用严格模式（所有路由都需要权限检查） */
  strictMode: boolean
  /** 权限检查失败时的处理方式 */
  onPermissionDenied: 'redirect' | 'throw' | 'silent'
  /** 自定义权限检查函数 */
  customPermissionCheck?: (
    requiredPermissions: Permission[],
    userPermissions: Permission[]
  ) => boolean
}
