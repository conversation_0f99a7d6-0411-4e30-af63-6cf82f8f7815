import { cache } from './cache'
import type { 
  Permission, 
  UserRole, 
  PermissionManager, 
  PermissionCheckResult,
  RoutePermission,
  PermissionCacheConfig,
  PermissionChangeEvent
} from '@/types/auth'

/**
 * 权限管理器实现
 */
class PermissionManagerImpl implements PermissionManager {
  private userPermissions: Permission[] = []
  private userRoles: UserRole[] = []
  private routePermissions: Map<string, RoutePermission> = new Map()
  private eventListeners: Map<string, ((event: PermissionChangeEvent) => void)[]> = new Map()
  
  private cacheConfig: PermissionCacheConfig = {
    permissionCacheTTL: 30 * 60 * 1000, // 30分钟
    roleCacheTTL: 60 * 60 * 1000, // 1小时
    enableCache: true,
    cacheKeyPrefix: 'permission_'
  }

  /**
   * 设置用户权限
   */
  setUserPermissions(permissions: Permission[]): void {
    this.userPermissions = [...permissions]
    
    if (this.cacheConfig.enableCache) {
      cache.set(`${this.cacheConfig.cacheKeyPrefix}user_permissions`, permissions, {
        key: `${this.cacheConfig.cacheKeyPrefix}user_permissions`,
        ttl: this.cacheConfig.permissionCacheTTL
      })
    }

    this.emitEvent({
      type: 'permission_added',
      payload: { permissions },
      timestamp: Date.now()
    })
  }

  /**
   * 设置用户角色
   */
  setUserRoles(roles: UserRole[]): void {
    this.userRoles = [...roles]
    
    if (this.cacheConfig.enableCache) {
      cache.set(`${this.cacheConfig.cacheKeyPrefix}user_roles`, roles, {
        key: `${this.cacheConfig.cacheKeyPrefix}user_roles`,
        ttl: this.cacheConfig.roleCacheTTL
      })
    }

    this.emitEvent({
      type: 'role_changed',
      payload: { roles },
      timestamp: Date.now()
    })
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: Permission): boolean {
    return this.userPermissions.includes(permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(role: UserRole): boolean {
    return this.userRoles.includes(role)
  }

  /**
   * 检查用户是否有任一权限
   */
  hasAnyPermission(permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(permission))
  }

  /**
   * 检查用户是否有所有权限
   */
  hasAllPermissions(permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(permission))
  }

  /**
   * 获取用户权限列表
   */
  getUserPermissions(): Permission[] {
    if (this.cacheConfig.enableCache) {
      const cachedPermissions = cache.get<Permission[]>(`${this.cacheConfig.cacheKeyPrefix}user_permissions`)
      if (cachedPermissions) {
        this.userPermissions = cachedPermissions
      }
    }
    return [...this.userPermissions]
  }

  /**
   * 获取用户角色列表
   */
  getUserRoles(): UserRole[] {
    if (this.cacheConfig.enableCache) {
      const cachedRoles = cache.get<UserRole[]>(`${this.cacheConfig.cacheKeyPrefix}user_roles`)
      if (cachedRoles) {
        this.userRoles = cachedRoles
      }
    }
    return [...this.userRoles]
  }

  /**
   * 注册路由权限配置
   */
  registerRoutePermission(routePermission: RoutePermission): void {
    this.routePermissions.set(routePermission.path, routePermission)
  }

  /**
   * 批量注册路由权限配置
   */
  registerRoutePermissions(routePermissions: RoutePermission[]): void {
    routePermissions.forEach(permission => {
      this.registerRoutePermission(permission)
    })
  }

  /**
   * 检查路由权限
   */
  checkRoutePermission(routePath: string): PermissionCheckResult {
    const routePermission = this.routePermissions.get(routePath)
    
    // 如果没有配置权限，默认允许访问
    if (!routePermission) {
      return { hasPermission: true }
    }

    // 公开路由，无需权限检查
    if (routePermission.isPublic) {
      return { hasPermission: true }
    }

    // 检查是否需要登录
    if (routePermission.requireAuth && this.userPermissions.length === 0) {
      return {
        hasPermission: false,
        message: '需要登录才能访问此页面'
      }
    }

    const result: PermissionCheckResult = { hasPermission: true }

    // 检查角色权限
    if (routePermission.roles && routePermission.roles.length > 0) {
      const hasRequiredRole = routePermission.roles.some(role => this.hasRole(role))
      if (!hasRequiredRole) {
        result.hasPermission = false
        result.missingRoles = routePermission.roles.filter(role => !this.hasRole(role))
        result.message = '您的角色权限不足，无法访问此页面'
      }
    }

    // 检查具体权限
    if (routePermission.permissions && routePermission.permissions.length > 0) {
      const missingPermissions = routePermission.permissions.filter(
        permission => !this.hasPermission(permission)
      )
      
      if (missingPermissions.length > 0) {
        result.hasPermission = false
        result.missingPermissions = missingPermissions
        result.message = '您缺少必要的权限，无法访问此页面'
      }
    }

    return result
  }

  /**
   * 清除用户权限和角色
   */
  clearUserPermissions(): void {
    this.userPermissions = []
    this.userRoles = []
    
    if (this.cacheConfig.enableCache) {
      cache.delete(`${this.cacheConfig.cacheKeyPrefix}user_permissions`)
      cache.delete(`${this.cacheConfig.cacheKeyPrefix}user_roles`)
    }

    this.emitEvent({
      type: 'user_logout',
      payload: {},
      timestamp: Date.now()
    })
  }

  /**
   * 监听权限变更事件
   */
  on(event: string, listener: (event: PermissionChangeEvent) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  /**
   * 移除权限变更事件监听
   */
  off(event: string, listener: (event: PermissionChangeEvent) => void): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发权限变更事件
   */
  private emitEvent(event: PermissionChangeEvent): void {
    const listeners = this.eventListeners.get(event.type) || []
    const allListeners = this.eventListeners.get('*') || []
    
    const combinedListeners = listeners.concat(allListeners)
    combinedListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.warn('Permission event listener error:', error)
      }
    })
  }

  /**
   * 更新缓存配置
   */
  updateCacheConfig(config: Partial<PermissionCacheConfig>): void {
    this.cacheConfig = { ...this.cacheConfig, ...config }
  }

  /**
   * 获取路由权限配置
   */
  getRoutePermission(routePath: string): RoutePermission | undefined {
    return this.routePermissions.get(routePath)
  }

  /**
   * 获取所有路由权限配置
   */
  getAllRoutePermissions(): RoutePermission[] {
    return Array.from(this.routePermissions.values())
  }
}

// 创建权限管理器实例
export const permissionManager = new PermissionManagerImpl()

// 导出便捷函数
export const hasPermission = (permission: Permission): boolean => {
  return permissionManager.hasPermission(permission)
}

export const hasRole = (role: UserRole): boolean => {
  return permissionManager.hasRole(role)
}

export const hasAnyPermission = (permissions: Permission[]): boolean => {
  return permissionManager.hasAnyPermission(permissions)
}

export const hasAllPermissions = (permissions: Permission[]): boolean => {
  return permissionManager.hasAllPermissions(permissions)
}

export const checkRoutePermission = (routePath: string): PermissionCheckResult => {
  return permissionManager.checkRoutePermission(routePath)
}
