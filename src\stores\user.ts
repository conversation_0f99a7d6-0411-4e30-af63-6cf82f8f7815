import { defineStore } from "pinia"
import { ref, computed } from "vue"
import type { User } from "@/types/user"
import type { Permission, UserRole, LoginResponse } from "@/types/auth"
import { permissionManager } from "@/utils/permission"
import { getPermissionsByRoles } from "@/config/routes"

export const useUserStore = defineStore("user", () => {
  const user = ref<User | null>(null)
  const token = ref<string>("")
  const permissions = ref<Permission[]>([])
  const roles = ref<UserRole[]>([])
  const refreshToken = ref<string>("")

  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => user.value?.name || "Guest")
  const userRoles = computed(() => roles.value)
  const userPermissions = computed(() => permissions.value)

  const setUser = (userData: User) => {
    user.value = userData
  }

  const setToken = (tokenValue: string) => {
    token.value = tokenValue
    if (typeof window !== "undefined") {
      localStorage.setItem("token", tokenValue)
    }
  }

  const setRefreshToken = (tokenValue: string) => {
    refreshToken.value = tokenValue
    if (typeof window !== "undefined") {
      localStorage.setItem("refreshToken", tokenValue)
    }
  }

  const setPermissions = (userPermissions: Permission[]) => {
    permissions.value = [...userPermissions]
    permissionManager.setUserPermissions(userPermissions)
  }

  const setRoles = (userRoles: UserRole[]) => {
    roles.value = [...userRoles]
    permissionManager.setUserRoles(userRoles)

    // 根据角色自动设置权限
    const rolePermissions = getPermissionsByRoles(userRoles)
    setPermissions(rolePermissions)
  }

  const login = (loginData: LoginResponse) => {
    setUser(loginData.user)
    setToken(loginData.accessToken)

    if (loginData.refreshToken) {
      setRefreshToken(loginData.refreshToken)
    }

    setRoles(loginData.user.roles)
    setPermissions(loginData.user.permissions)
  }

  const logout = () => {
    user.value = null
    token.value = ""
    refreshToken.value = ""
    permissions.value = []
    roles.value = []

    // 清除权限管理器中的权限
    permissionManager.clearUserPermissions()

    if (typeof window !== "undefined") {
      localStorage.removeItem("token")
      localStorage.removeItem("refreshToken")
      localStorage.removeItem("userInfo")
    }
  }

  const initAuth = () => {
    if (typeof window !== "undefined") {
      const savedToken = localStorage.getItem("token")
      const savedRefreshToken = localStorage.getItem("refreshToken")
      const savedUserInfo = localStorage.getItem("userInfo")

      if (savedToken) {
        token.value = savedToken
      }

      if (savedRefreshToken) {
        refreshToken.value = savedRefreshToken
      }

      if (savedUserInfo) {
        try {
          const userInfo = JSON.parse(savedUserInfo)
          user.value = userInfo.user
          setRoles(userInfo.roles || [])
          setPermissions(userInfo.permissions || [])
        } catch (error) {
          console.error('Failed to parse saved user info:', error)
        }
      }
    }
  }

  const hasPermission = (permission: Permission): boolean => {
    return permissions.value.includes(permission)
  }

  const hasRole = (role: UserRole): boolean => {
    return roles.value.includes(role)
  }

  const hasAnyPermission = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.every(permission => hasPermission(permission))
  }

  const updateUserInfo = (userInfo: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userInfo }

      // 保存到本地存储
      if (typeof window !== "undefined") {
        localStorage.setItem("userInfo", JSON.stringify({
          user: user.value,
          roles: roles.value,
          permissions: permissions.value
        }))
      }
    }
  }

  return {
    // 状态
    user,
    token,
    refreshToken,
    permissions,
    roles,

    // 计算属性
    isLoggedIn,
    userName,
    userRoles,
    userPermissions,

    // 方法
    setUser,
    setToken,
    setRefreshToken,
    setPermissions,
    setRoles,
    login,
    logout,
    initAuth,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    updateUserInfo,
  }
})
