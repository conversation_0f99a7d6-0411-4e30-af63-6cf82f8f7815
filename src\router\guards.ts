import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores'
import { permissionManager } from '@/utils/permission'
import { routeGuardConfig, isWhiteListRoute, getRoutePermissionConfig } from '@/config/routes'
import type { PermissionCheckResult } from '@/types/auth'

/**
 * 路由守卫管理器
 */
class RouteGuardManager {
  private router: Router | null = null
  private loadingInstance: any = null

  /**
   * 安装路由守卫
   */
  install(router: Router): void {
    this.router = router
    this.setupBeforeEachGuard()
    this.setupAfterEachGuard()
    this.setupBeforeResolveGuard()
    this.setupErrorHandler()
  }

  /**
   * 设置前置守卫
   */
  private setupBeforeEachGuard(): void {
    if (!this.router) return

    this.router.beforeEach(async (to, from, next) => {
      // 显示加载状态
      this.showLoading()

      try {
        // 检查路由权限
        const permissionResult = await this.checkRoutePermission(to, from, next)
        if (!permissionResult) return // 权限检查已处理跳转

        // 权限检查通过，继续导航
        next()
      } catch (error) {
        console.error('Route guard error:', error)
        ElMessage.error('路由权限检查失败')
        next(false)
      } finally {
        this.hideLoading()
      }
    })
  }

  /**
   * 设置后置守卫
   */
  private setupAfterEachGuard(): void {
    if (!this.router) return

    this.router.afterEach((to, from) => {
      // 隐藏加载状态
      this.hideLoading()

      // 更新页面标题
      this.updatePageTitle(to)

      // 记录路由访问日志
      this.logRouteAccess(to, from)
    })
  }

  /**
   * 设置解析守卫
   */
  private setupBeforeResolveGuard(): void {
    if (!this.router) return

    this.router.beforeResolve(async (to, from, next) => {
      // 在路由解析之前进行最后的权限检查
      const userStore = useUserStore()
      
      if (!userStore.isLoggedIn && !isWhiteListRoute(to.path)) {
        ElMessage.warning('请先登录')
        next({ path: routeGuardConfig.loginPath, query: { redirect: to.fullPath } })
        return
      }

      next()
    })
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandler(): void {
    if (!this.router) return

    this.router.onError((error) => {
      console.error('Router error:', error)
      ElMessage.error('路由加载失败')
      
      // 可以在这里添加错误上报逻辑
      this.reportError(error)
    })
  }

  /**
   * 检查路由权限
   */
  private async checkRoutePermission(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): Promise<boolean> {
    const userStore = useUserStore()
    const routePath = to.path

    // 检查是否在白名单中
    if (isWhiteListRoute(routePath)) {
      return true
    }

    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      next({ 
        path: routeGuardConfig.loginPath, 
        query: { redirect: to.fullPath } 
      })
      return false
    }

    // 获取路由权限配置
    const routePermissionConfig = getRoutePermissionConfig(routePath)
    if (!routePermissionConfig) {
      // 没有配置权限，默认允许访问
      return true
    }

    // 检查路由权限
    const permissionResult: PermissionCheckResult = permissionManager.checkRoutePermission(routePath)
    
    if (!permissionResult.hasPermission) {
      this.handlePermissionDenied(permissionResult, to, next)
      return false
    }

    return true
  }

  /**
   * 处理权限被拒绝的情况
   */
  private handlePermissionDenied(
    result: PermissionCheckResult,
    to: RouteLocationNormalized,
    next: NavigationGuardNext
  ): void {
    const message = result.message || '您没有权限访问此页面'
    
    ElMessage.error(message)
    
    // 记录权限拒绝日志
    console.warn('Permission denied:', {
      path: to.path,
      missingPermissions: result.missingPermissions,
      missingRoles: result.missingRoles,
      message: result.message
    })

    // 根据配置决定跳转行为
    if (routeGuardConfig.noPermissionPath) {
      next({ 
        path: routeGuardConfig.noPermissionPath,
        query: { 
          from: to.fullPath,
          reason: 'permission_denied'
        }
      })
    } else {
      // 回到上一页或默认页面
      next(false)
    }
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    if (this.loadingInstance) return

    this.loadingInstance = ElLoading.service({
      lock: true,
      text: '页面加载中...',
      background: 'rgba(0, 0, 0, 0.1)',
      spinner: 'el-icon-loading'
    })
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(): void {
    if (this.loadingInstance) {
      this.loadingInstance.close()
      this.loadingInstance = null
    }
  }

  /**
   * 更新页面标题
   */
  private updatePageTitle(to: RouteLocationNormalized): void {
    const title = to.meta?.title as string
    if (title) {
      document.title = `${title} - Vue3 Template`
    } else {
      document.title = 'Vue3 Template'
    }
  }

  /**
   * 记录路由访问日志
   */
  private logRouteAccess(to: RouteLocationNormalized, from: RouteLocationNormalized): void {
    const userStore = useUserStore()
    
    const logData = {
      timestamp: new Date().toISOString(),
      userId: userStore.user?.id,
      fromPath: from.path,
      toPath: to.path,
      query: to.query,
      params: to.params
    }

    // 这里可以发送到日志服务
    console.log('Route access log:', logData)
  }

  /**
   * 错误上报
   */
  private reportError(error: Error): void {
    // 这里可以集成错误监控服务，如 Sentry
    console.error('Route error reported:', error)
  }

  /**
   * 动态添加路由权限
   */
  addRoutePermission(path: string, permissions: string[], roles: string[]): void {
    permissionManager.registerRoutePermission({
      path,
      permissions: permissions as any[],
      roles: roles as any[],
      requireAuth: true
    })
  }

  /**
   * 刷新用户权限
   */
  async refreshUserPermissions(): Promise<void> {
    const userStore = useUserStore()
    
    try {
      // 这里应该调用 API 获取最新的用户权限
      // const userPermissions = await getUserPermissions()
      // permissionManager.setUserPermissions(userPermissions.permissions)
      // permissionManager.setUserRoles(userPermissions.roles)
      
      console.log('User permissions refreshed')
    } catch (error) {
      console.error('Failed to refresh user permissions:', error)
      ElMessage.error('权限刷新失败')
    }
  }

  /**
   * 检查当前路由权限
   */
  checkCurrentRoutePermission(): boolean {
    if (!this.router) return false
    
    const currentRoute = this.router.currentRoute.value
    const result = permissionManager.checkRoutePermission(currentRoute.path)
    
    return result.hasPermission
  }

  /**
   * 获取用户可访问的路由列表
   */
  getAccessibleRoutes(): string[] {
    const allRoutes = this.router?.getRoutes() || []
    const accessibleRoutes: string[] = []

    allRoutes.forEach(route => {
      if (route.path && route.path !== '*') {
        const result = permissionManager.checkRoutePermission(route.path)
        if (result.hasPermission) {
          accessibleRoutes.push(route.path)
        }
      }
    })

    return accessibleRoutes
  }
}

// 创建路由守卫管理器实例
export const routeGuardManager = new RouteGuardManager()

// 导出安装函数
export const setupRouteGuards = (router: Router): void => {
  routeGuardManager.install(router)
}

// 导出便捷函数
export const refreshUserPermissions = (): Promise<void> => {
  return routeGuardManager.refreshUserPermissions()
}

export const checkCurrentRoutePermission = (): boolean => {
  return routeGuardManager.checkCurrentRoutePermission()
}

export const getAccessibleRoutes = (): string[] => {
  return routeGuardManager.getAccessibleRoutes()
}
