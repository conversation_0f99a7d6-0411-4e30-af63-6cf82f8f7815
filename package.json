{"name": "vue3-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@rushstack/eslint-patch": "1.12.0", "axios": "^1.11.0", "dayjs": "^1.11.18", "echarts": "^6.0.0", "element-plus": "^2.11.2", "path": "0.12.7", "pinia": "^3.0.3", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "vue": "^3.5.21", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.35.0", "eslint-plugin-vue": "^10.4.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "sass": "^1.92.1", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.5", "vue-tsc": "^3.0.6"}}