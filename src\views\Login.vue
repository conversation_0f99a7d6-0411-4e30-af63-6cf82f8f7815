<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <div class="logo-icon">V3</div>
          <h1>Vue3 Template</h1>
        </div>
        <p class="subtitle">欢迎回来，请登录您的账户</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名或邮箱"
            size="large"
            prefix-icon="User"
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="Lock"
            show-password
            :disabled="loading"
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-link type="primary" :underline="false">忘记密码？</el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="demo-accounts">
        <div class="demo-title">演示账户</div>
        <div class="demo-account-list">
          <div 
            v-for="account in demoAccounts" 
            :key="account.role"
            class="demo-account"
            @click="fillDemoAccount(account)"
          >
            <div class="account-role">{{ account.roleLabel }}</div>
            <div class="account-info">{{ account.username }}</div>
          </div>
        </div>
      </div>

      <div class="login-footer">
        <p>还没有账户？ <el-link type="primary" :underline="false">立即注册</el-link></p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { useUserStore } from '@/stores'
import { UserRole, Permission } from '@/types/auth'
import type { LoginResponse } from '@/types/auth'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loginFormRef = ref<InstanceType<typeof ElForm>>()
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 演示账户
const demoAccounts = [
  {
    role: UserRole.SUPER_ADMIN,
    roleLabel: '超级管理员',
    username: 'superadmin',
    password: '123456'
  },
  {
    role: UserRole.ADMIN,
    roleLabel: '管理员',
    username: 'admin',
    password: '123456'
  },
  {
    role: UserRole.USER,
    roleLabel: '普通用户',
    username: 'user',
    password: '123456'
  },
  {
    role: UserRole.GUEST,
    roleLabel: '访客',
    username: 'guest',
    password: '123456'
  }
]

const fillDemoAccount = (account: typeof demoAccounts[0]) => {
  loginForm.username = account.username
  loginForm.password = account.password
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 模拟登录 API 调用
    const loginResponse = await mockLogin(loginForm.username, loginForm.password)
    
    // 使用 store 的 login 方法
    userStore.login(loginResponse)

    ElMessage.success('登录成功')

    // 跳转到目标页面或默认页面
    const redirect = route.query.redirect as string || '/dashboard'
    router.push(redirect)

  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 模拟登录 API
const mockLogin = async (username: string, password: string): Promise<LoginResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))

  const account = demoAccounts.find(acc => acc.username === username)
  
  if (!account || password !== account.password) {
    throw new Error('用户名或密码错误')
  }

  // 根据角色返回不同的权限
  const getPermissionsByRole = (role: UserRole): Permission[] => {
    switch (role) {
      case UserRole.SUPER_ADMIN:
        return Object.values(Permission)
      case UserRole.ADMIN:
        return [
          Permission.USER_VIEW,
          Permission.USER_CREATE,
          Permission.USER_UPDATE,
          Permission.SYSTEM_MONITOR,
          Permission.DATA_VIEW,
          Permission.DATA_EXPORT,
          Permission.DATA_IMPORT,
          Permission.CACHE_VIEW,
          Permission.CACHE_MANAGE
        ]
      case UserRole.USER:
        return [
          Permission.DATA_VIEW,
          Permission.CACHE_VIEW
        ]
      case UserRole.GUEST:
        return [Permission.DATA_VIEW]
      default:
        return []
    }
  }

  return {
    accessToken: `mock_token_${Date.now()}`,
    refreshToken: `mock_refresh_token_${Date.now()}`,
    expiresIn: 3600,
    user: {
      id: `user_${Date.now()}`,
      username: account.username,
      name: account.roleLabel,
      email: `${account.username}@example.com`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${account.username}`,
      role: account.role,
      roles: [account.role],
      permissions: getPermissionsByRole(account.role),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active'
    }
  }
}

onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    const redirect = route.query.redirect as string || '/dashboard'
    router.push(redirect)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;

    .logo-icon {
      width: 48px;
      height: 48px;
      background: #409eff;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }

    h1 {
      margin: 0;
      font-size: 24px;
      color: #333;
    }
  }

  .subtitle {
    color: #666;
    margin: 0;
    font-size: 14px;
  }
}

.login-form {
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
  }
}

.demo-accounts {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  .demo-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
  }

  .demo-account-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .demo-account {
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #e4e7ed;

    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
    }

    .account-role {
      font-size: 12px;
      font-weight: 600;
      color: #409eff;
    }

    .account-info {
      font-size: 11px;
      color: #666;
      margin-top: 2px;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 20px;

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}
</style>
